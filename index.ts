import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import { PocketOption } from './src/broker/PocketOption'
import apiRoutes from './src/routes'

const app = express()
const port = process.env.PORT || 3000

app.use(express.json())

app.use(cors())
app.use(helmet())
app.use(morgan('dev'))

export const broker = new PocketOption(process.env.SSID as string, process.env.DEMO === 'false' ? false : true)

broker
	.connect()
	.then(() => {
		console.log(`[Broker] Connected successfully`)
	})
	.catch((err: Error) => {
		console.error(`[Broker] Connection failed: ${err.message}\r\n${err.stack}`)
		process.exit(1)
	})

app.use(`/api`, apiRoutes)

app.listen(port, () => {
	console.log(`Server started on port ${port}`)
})
